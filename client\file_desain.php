<?php
require_once '../includes/session_manager.php';
require '../koneksi.php';

// Validasi session client
check_session_auth('client');

// Ambil client_id dari session
$client_id = intval($_SESSION['id_client']);

$page_title = "File Desain";
include 'includes/header/header.php';
?>

<?php include 'includes/sidebar/sidebar.php'; ?>

<div id="content-wrapper" class="d-flex flex-column">
    <div id="content">
        <?php include 'includes/topbar/topbar.php'; ?>

        <div class="container-fluid">
            <div class="d-sm-flex align-items-center justify-content-between mb-4">
                <h1 class="h3 mb-0 text-gray-800">File Desain Disetujui</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0 bg-transparent p-0">
                        <li class="breadcrumb-item"><a href="client.php">Dashboard</a></li>
                        <li class="breadcrumb-item active">File Desain</li>
                    </ol>
                </nav>
            </div>

            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle mr-2"></i>
                <strong>Informasi:</strong> Halaman ini menampilkan file desain yang sudah melalui proses verifikasi dan disetujui untuk proyek Anda.
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <?php
            $total_files = mysqli_num_rows(mysqli_query($koneksi, "SELECT * FROM file_gambar WHERE status_verifikasi = 'approved' AND client_id = '$client_id'"));
            $recent_files = mysqli_num_rows(mysqli_query($koneksi, "SELECT * FROM file_gambar WHERE status_verifikasi = 'approved' AND client_id = '$client_id' AND DATE(tanggal_verifikasi) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)"));
            ?>

            <div class="row mb-4">
                <!-- Total -->
                <div class="col-xl-6 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total File Disetujui</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_files; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-file-image fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Baru -->
                <div class="col-xl-6 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">File Baru (7 Hari)</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $recent_files; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Galeri -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary"><i class="fas fa-images mr-2"></i>Galeri File Desain</h6>
                </div>
                <div class="card-body">
                    <?php
                    $sql = mysqli_query($koneksi, "
                        SELECT fg.*, p.nama_petugas
                        FROM file_gambar fg
                        LEFT JOIN petugas p ON fg.verifikator_id = p.id_petugas
                        WHERE fg.status_verifikasi = 'approved' AND fg.client_id = '$client_id'
                        ORDER BY fg.tanggal_verifikasi DESC
                    ");

                    if (mysqli_num_rows($sql) > 0):
                    ?>
                        <div id="gridView" class="file-grid">
                            <div class="row">
                                <?php while ($data = mysqli_fetch_array($sql)): ?>
                                    <?php
                                    $file_ext = strtolower(pathinfo($data['gambar'], PATHINFO_EXTENSION));
                                    $file_icon = 'fas fa-file';
                                    $is_image = false;

                                    if (in_array($file_ext, ['jpg', 'jpeg', 'png', 'gif'])) {
                                        $file_icon = 'fas fa-file-image';
                                        $is_image = true;
                                    } elseif ($file_ext == 'pdf') {
                                        $file_icon = 'fas fa-file-pdf';
                                    } elseif (in_array($file_ext, ['dwg', 'obj', 'stl'])) {
                                        $file_icon = 'fas fa-cube';
                                    }
                                    ?>
                                    <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12 mb-4">
                                        <div class="card file-card h-100">
                                            <div class="card-body text-center">
                                                <?php if ($is_image): ?>
                                                    <div class="file-preview mb-3">
                                                        <img src="../file_proyek/<?php echo $data['gambar']; ?>"
                                                            class="img-fluid rounded"
                                                            style="max-height: 150px; object-fit: cover;"
                                                            alt="<?php echo htmlspecialchars($data['deskripsi']); ?>">
                                                    </div>
                                                <?php else: ?>
                                                    <div class="file-icon mb-3">
                                                        <i class="<?php echo $file_icon; ?> fa-4x text-primary"></i>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <h6 class="card-title font-weight-bold"><?php echo htmlspecialchars($data['gambar']); ?></h6>
                                                <p class="card-text text-muted small"><?php echo htmlspecialchars(substr($data['deskripsi'], 0, 80)); ?></p>
                                                
                                                <div class="file-meta mb-3">
                                                    <small class="text-success d-block">
                                                        <i class="fas fa-check-circle mr-1"></i>
                                                        Disetujui: <?php echo date('d M Y', strtotime($data['tanggal_verifikasi'])); ?>
                                                    </small>
                                                    <small class="text-muted">Oleh: <?php echo htmlspecialchars($data['nama_petugas'] ?? 'Admin'); ?></small>
                                                </div>

                                                <div class="btn-group w-100" role="group">
                                                    <a href="../file_proyek/<?php echo $data['gambar']; ?>" target="_blank" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-eye"></i> Lihat
                                                    </a>
                                                    <a href="../file_proyek/<?php echo $data['gambar']; ?>" download class="btn btn-outline-success btn-sm">
                                                        <i class="fas fa-download"></i> Download
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-folder-open fa-3x text-gray-300 mb-3"></i>
                            <h5 class="text-gray-600">Belum Ada File yang Disetujui</h5>
                            <p class="text-muted">File desain yang disetujui akan ditampilkan di sini.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

<style>
.file-card {
    transition: all 0.3s ease;
    border: 1px solid #e3e6f0;
}
.file-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
.file-preview img {
    transition: transform 0.3s ease;
}
.file-card:hover .file-preview img {
    transform: scale(1.05);
}
.file-icon {
    opacity: 0.8;
}
.file-card:hover .file-icon {
    opacity: 1;
}
.file-meta {
    border-top: 1px solid #e3e6f0;
    padding-top: 10px;
}
@media (max-width: 768px) {
    .file-grid .col-xl-3,
    .file-grid .col-lg-4,
    .file-grid .col-md-6 {
        margin-bottom: 20px;
    }
}
</style>

<script>
function toggleView(viewType) {
    const gridView = document.getElementById('gridView');
    const listView = document.getElementById('listView');
    if (viewType === 'grid') {
        gridView.style.display = 'block';
        listView.style.display = 'none';
    } else {
        gridView.style.display = 'none';
        listView.style.display = 'block';
    }
}
</script>

<?php include 'includes/footer/footer.php'; ?>
