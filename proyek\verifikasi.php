<?php
require_once '../includes/session_manager.php';
check_session_auth(['proyek']);

$page_title = "Verifikasi";
include 'includes/header/header.php';
require '../koneksi.php'; 
?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<?php include 'includes/sidebar/sidebar.php'; ?>

<div id="content-wrapper" class="d-flex flex-column">
    <div id="content">
        <?php include 'includes/topbar/topbar.php'; ?>
        <div class="container-fluid">

            <div class="d-sm-flex align-items-center justify-content-between mb-4">
                <h1 class="h3 mb-0 text-gray-800">Verifikasi & Approval</h1>
            </div>

            <ul class="nav nav-tabs" id="verifikasiTab" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="tugas-tab" data-toggle="tab" href="#tugas" role="tab">
                        <i class="fas fa-tasks mr-2"></i>Verifikasi Tugas
                        <?php
                            $count_tugas_query = mysqli_query($koneksi, "SELECT COUNT(*) FROM tugas_proyek WHERE status_verifikasi = 'pending'");
                            $count_tugas = mysqli_fetch_row($count_tugas_query)[0];
                            if ($count_tugas > 0) echo '<span class="badge badge-warning ml-1">'.$count_tugas.'</span>';
                        ?>
                    </a>
                </li>
                <!-- <li class="nav-item">
                    <a class="nav-link" id="file-tab" data-toggle="tab" href="#file" role="tab">
                        <i class="fas fa-file-image mr-2"></i>Verifikasi File
                        <?php
                            $count_file_query = mysqli_query($koneksi, "SELECT COUNT(*) FROM file_gambar WHERE status_verifikasi = 'pending'");
                            $count_file = mysqli_fetch_row($count_file_query)[0];
                            if ($count_file > 0) echo '<span class="badge badge-warning ml-1">'.$count_file.'</span>';
                        ?>
                    </a>
                </li> -->
                </ul>

            <div class="tab-content" id="verifikasiTabContent">
                <div class="tab-pane fade show active" id="tugas" role="tabpanel">
                    <div class="card shadow mb-4 mt-3">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary"><i class="fas fa-clipboard-check mr-2"></i>Daftar Tugas Menunggu Verifikasi</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th class="text-center">No</th>
                                            <th>Nama Proyek</th>
                                            <th>Nama Tugas</th>
                                            <th>Untuk Client</th>
                                            <th class="text-center">Timeline</th>
                                            <th class="text-center">Tgl. Submit</th>
                                            <th class="text-center">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $sql_tugas = mysqli_query($koneksi, "
                                            SELECT tp.*, p.nama_proyek, u.first_name, u.last_name 
                                            FROM tugas_proyek tp
                                            JOIN proyek p ON tp.proyek_id = p.id
                                            JOIN users u ON p.client_id = u.id
                                            WHERE tp.status_verifikasi = 'pending' 
                                            ORDER BY tp.tanggal_submit DESC
                                        ");
                                        $no_tugas = 1;
                                        if (mysqli_num_rows($sql_tugas) > 0) {
                                            while ($tugas = mysqli_fetch_array($sql_tugas)) {
                                        ?>
                                        <tr>
                                            <td class="text-center align-middle"><?php echo $no_tugas++; ?></td>
                                            <td class="align-middle"><?php echo htmlspecialchars($tugas['nama_proyek']); ?></td>
                                            <td class="align-middle font-weight-bold"><?php echo htmlspecialchars($tugas['nama_kegiatan']); ?></td>
                                            <td class="align-middle"><i class="fas fa-user text-gray-400 mr-2"></i><?php echo htmlspecialchars($tugas['first_name'].' '.$tugas['last_name']); ?></td>
                                            <td class="text-center align-middle"><small><?php echo date('d M Y', strtotime($tugas['tgl_mulai'])); ?> - <?php echo date('d M Y', strtotime($tugas['tgl_selesai'])); ?></small></td>
                                            <td class="text-center align-middle"><?php echo date('d M Y, H:i', strtotime($tugas['tanggal_submit'])); ?></td>
                                            <td class="text-center align-middle">
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-success btn-sm" onclick="verifikasiTugas(<?php echo $tugas['id']; ?>, 'approved')" title="Setujui Tugas"><i class="fas fa-check"></i></button>
                                                    <button class="btn btn-danger btn-sm" onclick="verifikasiTugas(<?php echo $tugas['id']; ?>, 'rejected')" title="Tolak Tugas"><i class="fas fa-times"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php } } else { ?>
                                        <tr>
                                            <td colspan="7" class="text-center py-4">Tidak ada tugas yang menunggu verifikasi.</td>
                                        </tr>
                                        <?php } ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- File Verification Tab -->
                <!-- <div class="tab-pane fade" id="file" role="tabpanel">
                    <div class="card shadow mb-4 mt-3">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary"><i class="fas fa-file-image mr-2"></i>Daftar File Menunggu Verifikasi</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="fileTable" width="100%" cellspacing="0">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th class="text-center">No</th>
                                            <th>Nama File</th>
                                            <th>Deskripsi</th>
                                            <th>Client</th>
                                            <th class="text-center">Tanggal Upload</th>
                                            <th class="text-center">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $sql_file = mysqli_query($koneksi, "
                                            SELECT fg.*, u.first_name, u.last_name, u.username
                                            FROM file_gambar fg
                                            LEFT JOIN users u ON fg.client_id = u.id
                                            WHERE fg.status_verifikasi = 'pending'
                                            ORDER BY fg.tanggal_submit ASC
                                        ");

                                        if (mysqli_num_rows($sql_file) > 0) {
                                            $no = 1;
                                            while ($data_file = mysqli_fetch_array($sql_file)) {
                                                $file_ext = strtolower(pathinfo($data_file['gambar'], PATHINFO_EXTENSION));
                                                $file_icon = 'fas fa-file';
                                                if (in_array($file_ext, ['jpg', 'jpeg', 'png', 'gif'])) {
                                                    $file_icon = 'fas fa-file-image';
                                                } elseif ($file_ext == 'pdf') {
                                                    $file_icon = 'fas fa-file-pdf';
                                                } elseif (in_array($file_ext, ['dwg', 'obj', 'stl'])) {
                                                    $file_icon = 'fas fa-cube';
                                                }
                                        ?>
                                        <tr>
                                            <td class="text-center"><?php echo $no++; ?></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="<?php echo $file_icon; ?> text-primary mr-2"></i>
                                                    <span class="font-weight-bold"><?php echo htmlspecialchars($data_file['gambar']); ?></span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="text-truncate" title="<?php echo htmlspecialchars($data_file['deskripsi']); ?>">
                                                    <?php echo htmlspecialchars($data_file['deskripsi']); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($data_file['client_id']): ?>
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-user text-info mr-2"></i>
                                                        <div>
                                                            <div class="font-weight-bold"><?php echo htmlspecialchars($data_file['first_name'] . ' ' . $data_file['last_name']); ?></div>
                                                            <small class="text-muted">@<?php echo htmlspecialchars($data_file['username']); ?></small>
                                                        </div>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-warning"><i class="fas fa-exclamation-triangle mr-1"></i>Tidak ada client</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <small class="text-muted">
                                                    <?php echo date('d M Y', strtotime($data_file['tanggal_submit'])); ?>
                                                    <br>
                                                    <?php echo date('H:i', strtotime($data_file['tanggal_submit'])); ?>
                                                </small>
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group" role="group">
                                                    <?php
                                                    $file_path = $data_file['client_id'] ?
                                                        "../file_proyek/client_" . $data_file['client_id'] . "/" . $data_file['gambar'] :
                                                        "../file_proyek/" . $data_file['gambar'];
                                                    ?>
                                                    <a href="<?php echo $file_path; ?>" target="_blank" class="btn btn-sm btn-outline-info" title="Lihat File">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-success" onclick="verifikasiFile(<?php echo $data_file['id']; ?>, 'approved')" title="Setujui">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-danger" onclick="verifikasiFile(<?php echo $data_file['id']; ?>, 'rejected')" title="Tolak">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php
                                            }
                                        } else {
                                        ?>
                                        <tr>
                                            <td colspan="6" class="text-center py-4">
                                                <div class="text-center">
                                                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                                    <h5 class="text-gray-600">Semua File Sudah Diverifikasi</h5>
                                                    <p class="text-muted">Tidak ada file yang menunggu verifikasi saat ini.</p>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php } ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div> -->
            </div>
        </div> </div> <div class="modal fade" id="verifikasiModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="verifikasiModalLabel">Verifikasi Item</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <form id="verifikasiForm" method="POST" action="proses_verifikasi.php">
                    <div class="modal-body">
                        <input type="hidden" id="item_id" name="item_id">
                        <input type="hidden" id="item_type" name="item_type">
                        <input type="hidden" id="status_verifikasi" name="status_verifikasi">
                        <div class="alert" id="verifikasiInfo"><i class="fas fa-info-circle mr-2"></i><span id="verifikasiInfoText"></span></div>
                        <div class="form-group">
                            <label for="catatan" class="font-weight-bold">Catatan Verifikasi:</label>
                            <textarea class="form-control" id="catatan" name="catatan" rows="4"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Simpan Verifikasi</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <?php include 'includes/footer/footer.php'; ?>
</div> </div> <script>
// Notifikasi dari session
<?php if(isset($_SESSION['success_message_riwayat'])): ?>
    Swal.fire({ text: '<?php echo $_SESSION['success_message_riwayat']; ?>', ... });
    <?php unset($_SESSION['success_message_riwayat']); ?>
<?php endif; ?> 
<?php if(isset($_SESSION['error_message'])): ?>
    Swal.fire({ icon: 'error', title: 'Gagal!', text: '<?php echo $_SESSION['error_message']; ?>' });
    <?php unset($_SESSION['error_message']); ?>
<?php endif; ?>

// Fungsi untuk modal verifikasi
function verifikasiTugas(id, status) {
    document.getElementById('item_id').value = id;
    document.getElementById('item_type').value = 'tugas';
    document.getElementById('status_verifikasi').value = status;
    const isApproved = status === 'approved';
    const modalTitle = isApproved ? '<i class="fas fa-check-circle mr-2 text-success"></i>Setujui Tugas' : '<i class="fas fa-times-circle mr-2 text-danger"></i>Tolak Tugas';
    document.getElementById('verifikasiModalLabel').innerHTML = modalTitle;
    const infoText = isApproved ? 'Anda akan menyetujui tugas ini. Catatan (opsional).' : 'Anda akan menolak tugas ini. Wajib berikan alasan penolakan.';
    document.getElementById('verifikasiInfoText').textContent = infoText;
    document.getElementById('catatan').required = !isApproved; 
    const alertDiv = document.getElementById('verifikasiInfo');
    alertDiv.className = isApproved ? 'alert alert-success' : 'alert alert-warning';
    document.getElementById('catatan').value = '';
    $('#verifikasiModal').modal('show');
}
function verifikasiFile(id, status) {
    document.getElementById('item_id').value = id;
    document.getElementById('item_type').value = 'file';
    document.getElementById('status_verifikasi').value = status;
    const isApproved = status === 'approved';
    const modalTitle = isApproved ? '<i class="fas fa-check-circle mr-2 text-success"></i>Setujui File' : '<i class="fas fa-times-circle mr-2 text-danger"></i>Tolak File';
    document.getElementById('verifikasiModalLabel').innerHTML = modalTitle;
    const infoText = isApproved ? 'Anda akan menyetujui file ini. Catatan (opsional).' : 'Anda akan menolak file ini. Wajib berikan alasan penolakan.';
    document.getElementById('verifikasiInfoText').textContent = infoText;
    document.getElementById('catatan').required = !isApproved;
    const alertDiv = document.getElementById('verifikasiInfo');
    alertDiv.className = isApproved ? 'alert alert-success' : 'alert alert-warning';
    document.getElementById('catatan').value = '';
    $('#verifikasiModal').modal('show');
}
</script>

</body>
</html>