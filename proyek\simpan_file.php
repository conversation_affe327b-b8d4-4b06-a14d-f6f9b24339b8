<?php
require '../koneksi.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $deskripsi = mysqli_real_escape_string($koneksi, $_POST['deskripsi']);
    $client_id = (int)$_POST['client_id'];
    $file = $_FILES['gambar'];

    // Validate client_id
    if (empty($client_id)) {
        echo "<script>alert('Client harus dipilih!'); window.history.back();</script>";
        exit();
    }

    // Verify client exists
    $client_check = mysqli_query($koneksi, "SELECT id, first_name, last_name FROM users WHERE id = $client_id AND role = 'client'");
    if (mysqli_num_rows($client_check) == 0) {
        echo "<script>alert('Client tidak valid!'); window.history.back();</script>";
        exit();
    }

    $namaFile = basename($file['name']);
    // Create client-specific directory structure
    $targetDir = "../file_proyek/client_" . $client_id . "/";
    $namaUnik = time() . "_" . preg_replace("/[^a-zA-Z0-9.\-_]/", "_", $namaFile);
    $targetFile = $targetDir . $namaUnik;

    $allowedExt = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'obj', 'stl', 'dwg'];
    $fileExt = strtolower(pathinfo($namaFile, PATHINFO_EXTENSION));

    if (in_array($fileExt, $allowedExt)) {
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0777, true); // Bikin folder jika belum ada
        }

        if (move_uploaded_file($file['tmp_name'], $targetFile)) {
            // Insert dengan client_id dan status_verifikasi default 'pending'
            $stmt = mysqli_prepare($koneksi, "INSERT INTO file_gambar (client_id, deskripsi, gambar, status_verifikasi) VALUES (?, ?, ?, 'pending')");
            mysqli_stmt_bind_param($stmt, "iss", $client_id, $deskripsi, $namaUnik);

            if (mysqli_stmt_execute($stmt)) {
                $client_info = mysqli_fetch_assoc($client_check);
                $client_name = $client_info['first_name'] . ' ' . $client_info['last_name'];
                echo "<script>alert('File berhasil diupload untuk client: $client_name\\nFile akan masuk ke antrian verifikasi!'); window.location.href='file_approved.php';</script>";
            } else {
                echo "<script>alert('Upload berhasil, tapi gagal simpan ke database: " . mysqli_error($koneksi) . "'); window.history.back();</script>";
            }
            mysqli_stmt_close($stmt);
        } else {
            echo "<script>alert('Gagal meng-upload file ke server.'); window.history.back();</script>";
        }
    } else {
        echo "<script>alert('Tipe file .$fileExt tidak diperbolehkan.\\nFormat yang didukung: JPG, PNG, GIF, PDF, OBJ, STL, DWG'); window.history.back();</script>";
    }
}
?>