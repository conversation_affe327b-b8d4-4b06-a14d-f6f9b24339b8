<?php
require_once '../includes/session_manager.php';
// <PERSON>ya izinkan 'admin' untuk menjalankan skrip ini
check_session_auth('admin');
require_once '../koneksi.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // Ambil data dari form
    $proyek_id = $_POST['proyek_id'];
    $nama_kegiatan = trim($_POST['nama_kegiatan']);
    $deskripsi = trim($_POST['deskripsi']);
    $tgl_mulai = $_POST['tgl_mulai'];
    $tgl_selesai = $_POST['tgl_selesai'];

    // Validasi dasar
    if (empty($proyek_id) || empty($nama_kegiatan) || empty($tgl_mulai) || empty($tgl_selesai)) {
        $_SESSION['error_message'] = "Semua kolom wajib diisi!";
        // Redirect kembali ke halaman asal
        header("Location: " . $_SERVER['HTTP_REFERER']);
        exit();
    }

    // Admin input - status pending untuk verifikasi
    $status_verifikasi = 'pending';
    $verifikator_id = NULL;
    $tanggal_verifikasi = NULL;

    // Siapkan perintah SQL INSERT yang aman
    $sql = "INSERT INTO tugas_proyek 
                (proyek_id, nama_kegiatan, deskripsi, tgl_mulai, tgl_selesai, status_verifikasi, verifikator_id, tanggal_verifikasi) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = mysqli_prepare($koneksi, $sql);
    
    // Bind parameter ke statement
    mysqli_stmt_bind_param($stmt, "isssssis", $proyek_id, $nama_kegiatan, $deskripsi, $tgl_mulai, $tgl_selesai, $status_verifikasi, $verifikator_id, $tanggal_verifikasi);

    // Eksekusi dan siapkan notifikasi
    if (mysqli_stmt_execute($stmt)) {
        $_SESSION['success_message'] = "Tugas baru berhasil ditambahkan dan menunggu verifikasi.";
    } else {
        $_SESSION['error_message'] = "Gagal menyimpan tugas.";
    }

    mysqli_stmt_close($stmt);
    mysqli_close($koneksi);

    // Arahkan kembali ke form input admin
    header("Location: ../admin/input_tugas.php");
    exit();

} else {
    // Jika file diakses langsung, arahkan ke dashboard admin
    header("Location: ../admin/admin.php");
    exit();
}
?>