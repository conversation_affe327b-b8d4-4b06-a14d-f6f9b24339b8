<?php
/**
 * Migration Script: Organize existing files by client
 * This script will:
 * 1. Create client-specific directories
 * 2. Move existing files to appropriate client directories
 * 3. Handle files without client_id (orphaned files)
 */

require '../koneksi.php';

echo "<h2>Migration Script: Organizing Files by Client</h2>\n";
echo "<pre>\n";

// Get all files from database
$files_query = "SELECT fg.*, u.first_name, u.last_name, u.username 
                FROM file_gambar fg 
                LEFT JOIN users u ON fg.client_id = u.id 
                ORDER BY fg.client_id, fg.id";
$files_result = mysqli_query($koneksi, $files_query);

$base_dir = "../file_proyek/";
$orphaned_dir = $base_dir . "orphaned_files/";
$moved_count = 0;
$orphaned_count = 0;
$error_count = 0;

// Create orphaned files directory if it doesn't exist
if (!is_dir($orphaned_dir)) {
    mkdir($orphaned_dir, 0777, true);
    echo "Created orphaned files directory: $orphaned_dir\n";
}

while ($file = mysqli_fetch_assoc($files_result)) {
    $old_path = $base_dir . $file['gambar'];
    
    // Check if file exists in old location
    if (!file_exists($old_path)) {
        echo "WARNING: File not found: {$file['gambar']}\n";
        continue;
    }
    
    if ($file['client_id']) {
        // File has client_id - move to client-specific directory
        $client_dir = $base_dir . "client_" . $file['client_id'] . "/";
        $new_path = $client_dir . $file['gambar'];
        
        // Create client directory if it doesn't exist
        if (!is_dir($client_dir)) {
            mkdir($client_dir, 0777, true);
            echo "Created client directory: $client_dir\n";
        }
        
        // Move file
        if (rename($old_path, $new_path)) {
            echo "MOVED: {$file['gambar']} -> client_{$file['client_id']}/ (Client: {$file['first_name']} {$file['last_name']})\n";
            $moved_count++;
        } else {
            echo "ERROR: Failed to move {$file['gambar']}\n";
            $error_count++;
        }
    } else {
        // File has no client_id - move to orphaned directory
        $new_path = $orphaned_dir . $file['gambar'];
        
        if (rename($old_path, $new_path)) {
            echo "ORPHANED: {$file['gambar']} -> orphaned_files/ (No client assigned)\n";
            $orphaned_count++;
        } else {
            echo "ERROR: Failed to move orphaned file {$file['gambar']}\n";
            $error_count++;
        }
    }
}

echo "\n=== MIGRATION SUMMARY ===\n";
echo "Files moved to client directories: $moved_count\n";
echo "Files moved to orphaned directory: $orphaned_count\n";
echo "Errors encountered: $error_count\n";

if ($orphaned_count > 0) {
    echo "\nNOTE: $orphaned_count files were moved to orphaned_files/ directory.\n";
    echo "These files don't have client_id assigned. You may need to:\n";
    echo "1. Manually assign them to clients in the database\n";
    echo "2. Move them to appropriate client directories\n";
    echo "3. Or delete them if they're no longer needed\n";
}

echo "\n=== NEXT STEPS ===\n";
echo "1. Test the file upload functionality\n";
echo "2. Verify that existing approved files can be viewed/downloaded\n";
echo "3. Check that new uploads are properly organized by client\n";

echo "</pre>\n";
?>
